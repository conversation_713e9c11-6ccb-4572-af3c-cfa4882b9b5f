import { createContext, useContext, ReactNode } from "react";
import { useEpiSignaturePage, IHandleEpiSignaturePageHook } from "../hooks/epi/use-epi-signature-page.hook";

interface IEpiSignatureContextProps {
	children: ReactNode;
}

const EpiSignatureContext = createContext<IHandleEpiSignaturePageHook | undefined>(undefined);

export const EpiSignatureProvider = ({ children }: IEpiSignatureContextProps) => {
	const epiSignatureData = useEpiSignaturePage();

	return (
		<EpiSignatureContext.Provider value={epiSignatureData}>
			{children}
		</EpiSignatureContext.Provider>
	);
};

export const useEpiSignatureContext = (): IHandleEpiSignaturePageHook => {
	const context = useContext(EpiSignatureContext);
	if (!context) {
		throw new Error("useEpiSignatureContext must be used within EpiSignatureProvider");
	}
	return context;
};
