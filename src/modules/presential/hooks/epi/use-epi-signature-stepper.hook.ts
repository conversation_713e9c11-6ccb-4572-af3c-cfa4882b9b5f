import { useEpiSignatureContext } from "@/modules/presential/contexts/epi-signature-context";
import { Check, FileText, Shield, User } from "lucide-react";
import { useCallback, useMemo } from "react";

export interface IStepConfig {
	id: number;
	title: string;
	description: string;
	icon: React.ElementType;
	mobileTitle?: string;
	isOptional?: boolean;
}

export interface IHandleEpiSignatureStepperHook {
	currentStep: number;
	totalSteps: number;
	isLoading: boolean;
	canProceed: boolean;
	canGoBack: boolean;
	isFirstStep: boolean;
	isLastStep: boolean;
	progress: number;
	stepConfig: IStepConfig[];
	handleNextStep: () => void;
	handlePreviousStep: () => void;
	handleStepChange: (step: number) => void;
	handleReset: () => void;
	getStepStatus: (stepId: number) => "pending" | "active" | "completed";
	getCurrentStepConfig: () => IStepConfig | undefined;
}

export const useEpiSignatureStepper = (): IHandleEpiSignatureStepperHook => {
	const {
		currentStep,
		isLoading,
		canProceed,
		handleNextStep: nextStep,
		handlePreviousStep: previousStep,
		handleStepChange: changeStep,
		resetProcess,
	} = useEpiSignatureContext();

	const stepConfig: IStepConfig[] = useMemo(
		() => [
			{
				id: 1,
				title: "Termo",
				description: "Seleção de Termo",
				mobileTitle: "Termo",
				icon: FileText,
			},
			{
				id: 2,
				title: "EPIs",
				description: "Seleção de EPIs e quantidade",
				mobileTitle: "EPIs",
				icon: Shield,
			},
			{
				id: 3,
				title: "Dados",
				description: "Preenchimento de dados pessoais",
				mobileTitle: "Dados",
				icon: User,
			},
			{
				id: 4,
				title: "Assinatura",
				description: "Leitura do termo e assinatura",
				mobileTitle: "Assinar",
				icon: FileText,
			},
			{
				id: 5,
				title: "Confirmação",
				description: "Confirmação de dados",
				mobileTitle: "Confirmar",
				icon: Check,
			},
		],
		[]
	);

	const totalSteps = stepConfig.length;
	const isFirstStep = currentStep === 1;
	const isLastStep = currentStep === totalSteps;
	const canGoBack = !isFirstStep && !isLoading;
	const progress = (currentStep / totalSteps) * 100;

	const handleNextStep = useCallback(() => {
		if (canProceed && !isLastStep) {
			nextStep();
		}
	}, [canProceed, isLastStep, nextStep]);

	const handlePreviousStep = useCallback(() => {
		if (canGoBack) {
			previousStep();
		}
	}, [canGoBack, previousStep]);

	const handleStepChange = useCallback(
		(step: number) => {
			if (step >= 1 && step <= totalSteps && !isLoading) {
				changeStep(step);
			}
		},
		[totalSteps, isLoading, changeStep]
	);

	const handleReset = useCallback(() => {
		if (!isLoading) {
			resetProcess();
		}
	}, [isLoading, resetProcess]);

	const getStepStatus = useCallback(
		(stepId: number): "pending" | "active" | "completed" => {
			if (stepId < currentStep) return "completed";
			if (stepId === currentStep) return "active";
			return "pending";
		},
		[currentStep]
	);

	const getCurrentStepConfig = useCallback((): IStepConfig | undefined => {
		return stepConfig.find(step => step.id === currentStep);
	}, [stepConfig, currentStep]);

	return {
		currentStep,
		totalSteps,
		isLoading,
		canProceed,
		canGoBack,
		isFirstStep,
		isLastStep,
		progress,
		stepConfig,
		handleNextStep,
		handlePreviousStep,
		handleStepChange,
		handleReset,
		getStepStatus,
		getCurrentStepConfig,
	};
};
