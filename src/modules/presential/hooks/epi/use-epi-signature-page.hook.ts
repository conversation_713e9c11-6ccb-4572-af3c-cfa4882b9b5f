import { useCallback, useState } from "react";

export interface IEpiSignatureData {
	selectedPersonId?: number;
	selectedTermId?: number;
	selectedEpiGroupId?: number;
	selectedEpis?: any[];
	epiItems?: any[];
	personCpf?: string;
	personName?: string;
	photo?: File;
	signatoryCount?: number;
	signature?: string;
}

export interface IHandleEpiSignaturePageHook {
	currentStep: number;
	isLoading: boolean;
	canProceed: boolean;
	signatureData: IEpiSignatureData;
	handleNextStep: () => void;
	handlePreviousStep: () => void;
	handleStepChange: (step: number) => void;
	updateSignatureData: (data: Partial<IEpiSignatureData>) => void;
	resetProcess: () => void;
	validateCurrentStep: () => boolean;
}

export const useEpiSignaturePage = (): IHandleEpiSignaturePageHook => {
	const [currentStep, setCurrentStep] = useState(1);
	const [isLoading, setIsLoading] = useState(false);
	const [signatureData, setSignatureData] = useState<IEpiSignatureData>({});

	const validateCurrentStep = useCallback((): boolean => {
		console.log("🔍 Validating step:", currentStep, "signatureData:", signatureData);
		switch (currentStep) {
			case 1:
				// Validar se termo foi selecionado
				const hasTermSelected = !!signatureData.selectedTermId;
				console.log("📋 Step 1 - Term selected:", hasTermSelected, "selectedTermId:", signatureData.selectedTermId);
				return hasTermSelected;
			case 2:
				// Validar se EPIs foram selecionados e quantidade definida
				const hasEpisAndCount = !!(signatureData.selectedEpis?.length && signatureData.signatoryCount);
				console.log("🛡️ Step 2 - EPIs and count:", hasEpisAndCount);
				return hasEpisAndCount;
			case 3:
				// Validar se CPF, nome e foto foram preenchidos
				const hasPersonalData = !!(signatureData.personCpf && signatureData.personName && signatureData.photo);
				console.log("👤 Step 3 - Personal data:", hasPersonalData);
				return hasPersonalData;
			case 4:
				// Validar se assinatura foi realizada
				const hasSignature = !!signatureData.signature;
				console.log("✍️ Step 4 - Signature:", hasSignature);
				return hasSignature;
			case 5:
				// Etapa de confirmação - sempre válida se chegou até aqui
				console.log("✅ Step 5 - Confirmation: true");
				return true;
			default:
				console.log("❌ Invalid step:", currentStep);
				return false;
		}
	}, [currentStep, signatureData]);

	const canProceed = validateCurrentStep();
	console.log("🚀 Can proceed:", canProceed);

	const handleNextStep = useCallback(() => {
		if (canProceed && currentStep < 5) {
			setCurrentStep(prev => prev + 1);
		}
	}, [canProceed, currentStep]);

	const handlePreviousStep = useCallback(() => {
		if (currentStep > 1) {
			setCurrentStep(prev => prev - 1);
		}
	}, [currentStep]);

	const handleStepChange = useCallback((step: number) => {
		if (step >= 1 && step <= 5) {
			setCurrentStep(step);
		}
	}, []);

	const updateSignatureData = useCallback((data: Partial<IEpiSignatureData>) => {
		setSignatureData(prev => ({ ...prev, ...data }));
	}, []);

	const resetProcess = useCallback(() => {
		setCurrentStep(1);
		setSignatureData({});
		setIsLoading(false);
	}, []);

	return {
		currentStep,
		isLoading,
		canProceed,
		signatureData,
		handleNextStep,
		handlePreviousStep,
		handleStepChange,
		updateSignatureData,
		resetProcess,
		validateCurrentStep,
	};
};
