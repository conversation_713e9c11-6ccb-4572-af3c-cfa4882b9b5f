import { useEpiSignaturePage } from "@/modules/presential/hooks/epi/use-epi-signature-page.hook";
import { useFindAllTermsQuery } from "@/modules/presential/hooks/terms/find-all-terms-query.hook";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";
import { Card, CardContent } from "@/shared/components/ui/card";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { cn } from "@/shared/lib/utils";
import { Check, FileText } from "lucide-react";

const SectionHeader = () => (
	<div className="text-center mb-6">
		<div className="inline-flex items-center justify-center w-12 h-12 bg-pormade/10 rounded-xl mb-2">
			<FileText className="w-6 h-6 text-pormade" />
		</div>
		<h2 className="text-xl font-bold text-gray-900">Selecione o Termo</h2>
		<p className="text-gray-600 text-sm">Escolha o termo para assinatura de EPI</p>
	</div>
);

const LoadingSkeleton = () => (
	<div className="space-y-6">
		<SectionHeader />
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
			{Array.from({ length: 6 }).map((_, index) => (
				<Card key={index} className="h-32 p-4 animate-pulse">
					<div className="flex items-center gap-4 h-full">
						<Skeleton className="h-10 w-10 rounded-xl flex-shrink-0" />
						<div className="flex-1 space-y-3">
							<Skeleton className="h-4 w-full" />
							<Skeleton className="h-3 w-2/3" />
						</div>
					</div>
				</Card>
			))}
		</div>
	</div>
);

const EmptyState = () => (
	<Card className="text-center py-12 border-dashed border-2 border-gray-200 bg-gray-50/50">
		<CardContent className="space-y-4">
			<div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-2xl mx-auto">
				<FileText className="w-8 h-8 text-gray-400" />
			</div>
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhum termo encontrado</h3>
				<p className="text-gray-500 text-sm max-w-sm mx-auto">
					Não há termos disponíveis para seleção no momento. Entre em contato com o administrador.
				</p>
			</div>
		</CardContent>
	</Card>
);

interface TermCardProps {
	term: ITerm;
	isSelected: boolean;
	onSelect: (term: ITerm) => void;
}

const TermCard = ({ term, isSelected, onSelect }: TermCardProps) => (
	<Card
		className={cn(
			"cursor-pointer transition-shadow duration-200 hover:shadow-md",
			"border h-24 flex items-center px-4 relative",
			isSelected ? "border-pormade bg-pormade/5" : "border-gray-200 bg-white"
		)}
		onClick={() => onSelect(term)}
	>
		{isSelected && (
			<div className="absolute top-3 right-3">
				<div className="w-6 h-6 bg-pormade rounded-full flex items-center justify-center shadow">
					<Check className="w-4 h-4 text-white" />
				</div>
			</div>
		)}
		<CardContent className="flex items-center gap-4 p-0 w-full">
			<div className={cn("p-2 rounded-lg flex-shrink-0", isSelected ? "bg-pormade text-white" : "bg-gray-100 text-pormade")}>
				<FileText className="w-5 h-5" />
			</div>
			<div className="flex-1 min-w-0">
				<h3 className={cn("font-medium text-sm truncate", isSelected ? "text-pormade" : "text-gray-900")}>{term.title || term.fileName}</h3>
				<p className="text-xs text-gray-400">#{term.id}</p>
			</div>
		</CardContent>
	</Card>
);

interface SelectionConfirmationProps {
	selectedTerm: ITerm;
}

const SelectionConfirmation = ({ selectedTerm }: SelectionConfirmationProps) => (
	<div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl animate-in slide-in-from-top-2 duration-500">
		<div className="flex items-center gap-3">
			<div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
				<Check className="w-4 h-4 text-white" />
			</div>
			<div>
				<p className="text-sm font-semibold text-green-800">Termo selecionado</p>
				<p className="text-sm text-green-700">{selectedTerm.title || selectedTerm.fileName}</p>
			</div>
		</div>
	</div>
);

export const TermSelectionStep = () => {
	const { data: termsData, isLoading } = useFindAllTermsQuery();
	const { signatureData, updateSignatureData } = useEpiSignaturePage();

	const terms = termsData?.success ? termsData.data : [];
	const selectedTermId = signatureData.selectedTermId;
	const selectedTerm = terms.find(term => term.id === selectedTermId);

	const handleSelectTerm = (term: ITerm) => {
		console.log("🎯 Selecting term:", term);
		console.log("📝 Current signatureData before update:", signatureData);
		updateSignatureData({ selectedTermId: term.id });
		console.log("✅ Term selection completed");
	};

	if (isLoading) {
		return <LoadingSkeleton />;
	}

	return (
		<div className="space-y-6 max-w-6xl mx-auto">
			<SectionHeader />

			{terms.length === 0 ? (
				<EmptyState />
			) : (
				<>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						{terms.map(term => (
							<TermCard key={term.id} term={term} isSelected={selectedTermId === term.id} onSelect={handleSelectTerm} />
						))}
					</div>

					{selectedTermId && selectedTerm && <SelectionConfirmation selectedTerm={selectedTerm} />}
				</>
			)}
		</div>
	);
};
